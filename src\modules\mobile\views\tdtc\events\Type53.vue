<script>
import { type9 } from "@/mixins/tdtc/events/type9";

export default {
  mixins: [type9],
  methods: {
    select(date) {
      this.selectDay = date.getDate();
    },
    formatter(day) {
      const month = day.date.getMonth() + 1;
      const date = day.date.getDate();
      if (month === this.res.nowmonth) {
        this.res.info.forEach((row) => {
          if (row['signday'] === date) {
            day.bottomInfo = 'signed';
          }
        })
      }
      if (day.type === 'selected') {
        if (day.bottomInfo === 'signed') {
          this.signed = true
        } else {
          this.signed = false
        }
      }
      return day;
    },
  },

}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-signIn am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use>
              </svg></span></span>
        </div>
        <div class="am-navbar-title" style="font-size: .32rem !important;">{{ $t(`events_page.type.53`) }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="sigin-container"
      style="margin-bottom: 0.5rem;background-image: url('img/activity/19/bg.png');background-size: contain;background-repeat: no-repeat;width: 100%;min-height: 10.6rem;">
      <div class="light_box">
        <div v-for="index in 7" :key="index">
          <span>Ngày thứ {{ index }}</span>
          <img src="img/activity/19/on.png" alt="">
        </div>
      </div>
      <div class="light_action">
        <div>Tien thuong</div>
        <div>Tien thuong</div>
      </div>
      <div class="sigin-container" style="margin-bottom: 0.5rem;">
      <div
          class="mall-home-top signin-activity-card"
          style="background-image: url('/img/activity/10-1.png')"
      >
      </div>
      <div class="sigin-content" style="position: absolute">
        <div class="sigin-c-header">
          <div class="am-flexbox am-flexbox-align-middle">
            <div class="am-flexbox-item">
              <span class="sc-score sc-integral">{{ res['now_recharge'] | formatGold }}</span><br />
              <div class="sc-score-desc">{{$t('a6.cashback.table.current')}}</div>
            </div>
<!--            <div class="am-flexbox-item">
              <span class="sc-score sc-integral">{{ Details.maxReward | currency}}</span><br />
              <div class="sc-score-desc">{{ $t('Max Reward') }}</div>
            </div>
            <div class="am-flexbox-item">
              <span class="sc-score sc-integral">{{ Details.userFirstChargeamount | currency}}</span><br />
              <div class="sc-score-desc">{{ $t('First Deposit') }}</div>
            </div>-->
          </div>
        </div>
        <div class="sigin-content">
        </div>
        <div style="margin: -.5rem .2rem 0;">
          <van-button @click="!res['flag'] && submit()" type="warning" block round >{{ res['flag'] ? $t('claimed') : $t('claim') }}</van-button>
        </div>
        <div class="sigin-c-footer">
          <ul style="display: flex;justify-content: space-between;flex-wrap: wrap">
            <li v-for="(item, index) in res['conf']" :key="index">
              <div class="no-claimed">
                <span class="title" style="align-self: center;text-align: center">{{ item['award_score'] | formatGoldWithK }}
                </span>
                <div class="item-a si-item"></div>
                <div class="reward-content" style="text-align: center;">
                  {{ $t('recharge') }} ≥ {{ item['need_recharge'] | formatGold }}
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div class="sigin-c-remarks" v-if="currentActivity.awardType || currentActivity.withdrawRate">
          <p>
            <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
            <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
            <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
          </p>
          <p v-if="currentActivity.withdrawRate">
            <span class="ql-size-large">{{ $t('TIP_AWARD_RATE', {money: currentActivity.withdrawRate}) }}</span>
          </p>
        </div>
        <div class="sigin-c-remarks">
          <b class="sigin-rule">{{ $t('invite_rules') }}</b>
          <div>
            <div class="wysiwyg">
              <p>
                <span class="ql-size-large">
                  {{ $t('events_page.45.tip.0', [this.$options.filters['formatGold'](this.maxReward)]) }}
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    </div>
  </section>
</template>
<style scoped>
.finished {
  background-image: linear-gradient(0deg, #56cf30 0, #38a888 100%) !important;
}

.light_box {
  position: relative;
  height: 5.8rem;

  div {
    font-size: 0.16rem;
    color: #FFFFFF;
    display: flex;
    flex-direction: column;
    position: absolute;

    span {
      width: 0.93rem;
      height: 0.3rem;
      background: #B46656;
      border-radius: 0.06rem;
      margin-bottom: .2rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    img {
      width: 0.83rem;
      height: 1.35rem;
      box-shadow: 0rem 0rem 0rem 0rem rgba(0, 0, 0, 0.15);
    }
  }

  div:nth-child(1) {
    top: 1.45rem;
    left: 0.1rem;
  }

  div:nth-child(2) {
    top: 2.36rem;
    left: 1.8rem;
  }

  div:nth-child(3) {
    top: 1.5rem;
    left: 2.8rem;
  }

  div:nth-child(4) {
    top: 1.26rem;
    right: 1.8rem;
  }

  div:nth-child(5) {

    top: 0.53rem;
    right: 0.1rem;
  }

  div:nth-child(6) {
    top: 3.3rem;
    right: 0.4rem;
  }

  div:nth-child(7) {
    top: 3rem;
    left: 4rem;
  }
}

.light_action {
  display: flex;
  justify-content: space-around;
  align-items: center;
  color: #FFFFFF;
  padding: 0 .3rem;

  div {
    width: 3.09rem;
    height: 0.64rem;
    border-radius: 0.1rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  div:nth-child(1) {
    background: linear-gradient(90deg, #EC833A, #F9B606);
  }

  div:nth-child(2) {
    background: linear-gradient(90deg, #D31176, #F471DD);
  }
}
</style>